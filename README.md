# @seakoi/micro-web-sdk

> Web微前端主应用SDK，提供统一的服务接口和应用间通信能力

## 项目改造说明

本项目从原有的 vite+react 项目脱胎而来，经过以下改造：

### 任务1: 改造为vite打包
- ✅ 创建了 `vite.config.ts` 配置文件，设置为库模式
- ✅ 更新了 `package.json`，将rollup依赖替换为vite相关依赖
- ✅ 创建了适配vite的 `tsconfig.json` 配置
- ✅ 配置了TypeScript类型生成和路径映射

### 任务2: 移除共享组件功能
- ✅ 从SDK类中移除了所有组件相关的方法：
  - `registerComponent()`
  - `getComponent()`
  - `getAllComponents()`
- ✅ 更新了类型定义，移除了组件相关接口
- ✅ 更新了项目描述，移除了"组件共享"相关描述

## 核心功能

### 服务管理
- `registerService(name, service)` - 注册服务
- `getService(name)` - 获取服务
- `callMainMethod(serviceName, methodName, ...args)` - 调用主应用方法

### 基础功能
- `getUserInfo()` - 获取用户信息
- `getRouterInfo()` - 获取路由信息
- `navigateTo(path, options)` - 路由跳转
- `getThemeConfig()` - 获取主题配置

### 消息通信
- `message.sendToApp(appName, data)` - 发送消息到指定子应用
- `message.broadcast(data, callback)` - 发送全局消息
- `message.listenFromApp(appName, callback)` - 监听特定应用消息
- `message.listenGlobal(callback)` - 监听全局消息

### 消息中心
- `messageCenter.getMessages()` - 获取消息列表
- `messageCenter.markAsRead(ids)` - 标记消息为已读
- `messageCenter.getJumpTypes()` - 获取跳转类型枚举

### 权限管理
- `auth.hasPermission(permission)` - 检查权限
- `auth.getMenuPermissions()` - 获取菜单权限

## 安装

```bash
# 使用 pnpm (推荐)
pnpm add @seakoi/micro-web-sdk

# 或使用 npm
npm install @seakoi/micro-web-sdk

# 或使用 yarn
yarn add @seakoi/micro-web-sdk
```

## 使用

```typescript
import mainAppSDK from '@seakoi/micro-web-sdk';

// 注册服务
mainAppSDK.registerService('api', apiService);

// 获取用户信息
const userInfo = mainAppSDK.getUserInfo();

// 路由跳转
mainAppSDK.navigateTo('/dashboard', {
  label: '仪表板',
  closable: true
});

// 发送消息
mainAppSDK.message.broadcast({
  type: 'notification',
  message: 'Hello from main app!'
});

// 监听消息
const unsubscribe = mainAppSDK.message.listenFromApp('child-app', (data) => {
  console.log('Received message from child-app:', data);
});
```

## 构建

```bash
# 安装依赖
pnpm install

# 开发模式（监听文件变化）
pnpm dev

# 生产构建
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint
```

## 输出文件

构建后会生成以下文件：
- `dist/index.esm.js` - ES模块格式
- `dist/index.cjs.js` - CommonJS格式
- `dist/index.d.ts` - TypeScript类型定义文件

## 开发配置

### pnpm配置
项目已配置使用淘宝镜像源以加速依赖安装：
- 镜像源：`https://registry.npmmirror.com/`
- 工作区配置：`pnpm-workspace.yaml`
- 自动安装peer dependencies
- 优化的依赖提升策略

### 首次设置
```bash
# 安装pnpm (如果未安装)
npm install -g pnpm

# 克隆项目后
pnpm install

# 开始开发
pnpm dev
```

## 技术栈

- **包管理器**: pnpm (推荐)
- **构建工具**: Vite 5.x
- **语言**: TypeScript 5.x
- **类型生成**: vite-plugin-dts
- **代码压缩**: Terser

## 许可证

MIT 