# 变更日志

## [1.0.1] - 2024-01-31

### ESLint升级 🔧

#### ✅ 升级到ESLint 9.x
- **升级ESLint**: 从8.57.1升级到9.32.0
- **升级TypeScript插件**: 升级到最新版本8.38.0
- **新增代码风格插件**: 
  - `@stylistic/eslint-plugin` - 代码风格规则
  - `eslint-plugin-unicorn` - 现代JavaScript最佳实践
  - `eslint-plugin-check-file` - 文件命名规范
- **配置flat config**: 使用ESLint 9的新配置格式
- **文件重命名**: 所有文件遵循kebab-case命名规范
- **代码格式化**: 自动修复474个格式问题，统一代码风格

#### 配置特性
- **严格的代码风格**: 单引号、无分号、2空格缩进
- **文件命名规范**: 强制使用kebab-case
- **TypeScript优化**: 更好的类型检查和错误提示
- **现代化规则**: 符合2024年JavaScript/TypeScript最佳实践

## [1.0.0] - 2024-01-31

### 项目改造完成 🎉

#### ✅ 任务1: 改造为vite打包
- **创建 vite.config.ts**: 配置库模式，支持ES模块和CommonJS输出
- **更新 package.json**: 移除rollup依赖，添加vite相关依赖
- **创建 tsconfig.json**: 适配vite的TypeScript配置
- **配置类型生成**: 使用vite-plugin-dts生成TypeScript类型定义

#### ✅ 任务2: 移除共享组件功能
- **移除组件管理方法**: 
  - `registerComponent()`
  - `getComponent()`
  - `getAllComponents()`
- **更新类型定义**: 从`MainAppSDKInterface`中移除组件相关接口
- **更新项目描述**: 专注于服务接口和应用间通信

#### ✅ 任务3: 改为使用pnpm
- **配置淘宝镜像源**: 使用`https://registry.npmmirror.com/`加速依赖安装
- **创建pnpm工作区**: 添加`pnpm-workspace.yaml`配置
- **优化pnpm配置**: 在`.npmrc`中添加推荐配置
- **更新文档**: README中的安装和构建命令改为pnpm

### 新增功能
- **微前端通信**: 完整的应用间通信机制
- **服务管理**: 统一的服务注册和调用接口
- **权限管理**: 用户权限检查和菜单权限获取
- **消息中心**: 消息列表管理和状态更新
- **路由管理**: 路由信息获取和页面跳转

### 技术栈
- **包管理器**: pnpm (推荐)
- **构建工具**: Vite 5.x
- **语言**: TypeScript 5.x
- **类型生成**: vite-plugin-dts
- **代码检查**: ESLint + TypeScript ESLint
- **代码压缩**: Terser

### 输出文件
```
dist/
├── index.esm.js     # ES模块格式 (8.72 kB)
├── index.cjs.js     # CommonJS格式 (4.17 kB)
├── index.d.ts       # TypeScript类型定义
└── *.map           # Source map文件
```

### 开发命令
```bash
pnpm install        # 安装依赖
pnpm dev           # 开发模式（监听文件变化）
pnpm build         # 生产构建
pnpm test          # 运行测试
pnpm lint          # 代码检查
```

### 已创建的文件
- `vite.config.ts` - Vite构建配置
- `tsconfig.json` - TypeScript配置
- `pnpm-workspace.yaml` - pnpm工作区配置
- `.npmrc` - pnpm配置（包含淘宝镜像源）
- `.gitignore` - Git忽略规则
- `.eslintrc.js` - ESLint配置
- `README.md` - 项目文档
- `CHANGELOG.md` - 变更日志

### 核心SDK功能
- ✅ 服务注册和调用
- ✅ 用户信息获取
- ✅ 路由管理和跳转
- ✅ 主题配置获取
- ✅ 应用间消息通信
- ✅ 消息中心管理
- ✅ 权限验证
- ❌ 组件共享（已移除） 