{"name": "@seakoi/micro-web-sdk", "version": "1.0.0", "description": "Web微前端主应用SDK，提供统一的服务接口和应用间通信能力", "type": "module", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md", "LICENSE"], "scripts": {"dev": "vite build --watch", "build": "vite build", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "prepare": "pnpm run build", "prepublishOnly": "pnpm run test && pnpm run lint"}, "keywords": ["microfrontend", "micro-frontend", "web", "sdk", "react", "seakoi", "communication"], "author": {"name": "Seakoi Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/seakoi/micro-web-sdk.git"}, "bugs": {"url": "https://github.com/seakoi/micro-web-sdk/issues"}, "homepage": "https://github.com/seakoi/micro-web-sdk#readme", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@stylistic/eslint-plugin": "^5.2.2", "@types/jest": "^29.0.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-plugin-check-file": "^3.3.0", "eslint-plugin-unicorn": "^60.0.0", "jest": "^29.0.0", "terser": "^5.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-dts": "^3.0.0"}, "dependencies": {"@micro-zoe/micro-app": "1.0.0-rc.26", "lodash-es": "^4.17.21"}}