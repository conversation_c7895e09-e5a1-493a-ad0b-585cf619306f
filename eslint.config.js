import js from '@eslint/js'
import tseslint from '@typescript-eslint/eslint-plugin'
import tsparser from '@typescript-eslint/parser'
import stylistic from '@stylistic/eslint-plugin'
import unicorn from 'eslint-plugin-unicorn'
import checkFile from 'eslint-plugin-check-file'

export default [
  // 基础配置
  js.configs.recommended,
  
  // TypeScript文件配置
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tsparser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        project: './tsconfig.json',
      },
      globals: {
        window: 'readonly',
        document: 'readonly',
        console: 'readonly',
        process: 'readonly',
      },
    },
    plugins: {
      '@typescript-eslint': tseslint,
      '@stylistic': stylistic,
      unicorn,
      'check-file': checkFile,
    },
    rules: {
      // 基础规则
      'no-unused-vars': 'off', // 关闭基础规则，使用TS版本
      'no-undef': 'off', // TypeScript会处理未定义变量
      'no-console': 'warn',
      
      // TypeScript规则
      '@typescript-eslint/no-unused-vars': ['warn', { 
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_',
      }],
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',

      // 文件命名规则 - 强制使用 kebab-case
      'unicorn/filename-case': ['error', {
        case: 'kebabCase',
        ignore: [
          // 允许的例外文件
          'README.md',
          'CHANGELOG.md',
          'LICENSE',
          '\\.d\\.ts$',  // 类型定义文件
          '^[A-Z].*\\.md$',  // 大写开头的 markdown 文件
        ]
      }],

      // 文件夹命名规则 - 强制使用 kebab-case
      'check-file/folder-naming-convention': ['error', {
        '**/*': 'KEBAB_CASE',
      }],

      // Stylistic rules
      '@stylistic/indent': ['error', 2],
      '@stylistic/quotes': ['error', 'single'],
      '@stylistic/semi': ['error', 'never'],
      '@stylistic/comma-dangle': ['error', 'always-multiline'],
      '@stylistic/object-curly-spacing': ['error', 'always'],
      '@stylistic/arrow-parens': ['error', 'always'],
      '@stylistic/brace-style': ['error', '1tbs'],
      '@stylistic/type-annotation-spacing': ['error', {
        before: false,
        after: true,
        overrides: {
          arrow: {
            before: true,
            after: true,
          },
          colon: {
            before: false,
            after: true,
          },
        },
      }],
      // 等号、加号等运算符两侧必须有空格
      '@stylistic/space-infix-ops': ['error'],
      // 冒号前后空格控制
      '@stylistic/key-spacing': ['error', {
        beforeColon: false,
        afterColon: true,
        mode: 'strict'
      }],
    },
  },
  
  // 忽略的文件
  {
    ignores: [
      'dist/**',
      'node_modules/**',
      '*.config.js',
      '*.config.ts',
      'coverage/**',
    ],
  },
] 