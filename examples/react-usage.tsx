import React from 'react'
import { MicroAppProvider, useMicroApp, useMicroAppState } from '@seakoi/micro-web-sdk'

// 子应用示例：订单管理应用
function OrderApp() {
  return (
    <MicroAppProvider 
      appId="order-management"
      initialData={{ 
        currentModule: 'orders',
        permissions: ['read', 'write'] 
      }}
      onError={(error) => console.error('订单应用错误:', error)}
      onStateChange={(state) => console.log('订单应用状态变化:', state)}
    >
      <OrderLayout />
    </MicroAppProvider>
  )
}

// 应用布局组件
function OrderLayout() {
  const { appId, state, callMainMethod } = useMicroApp()
  
  const handleLogout = () => {
    callMainMethod('auth', 'logout')
  }
  
  return (
    <div>
      <header>
        <h1>订单管理系统 (App ID: {appId})</h1>
        <button onClick={handleLogout}>退出登录</button>
      </header>
      <main>
        <UserInfo />
        <OrderList />
        <MessageCenter />
      </main>
    </div>
  )
}

// 用户信息组件
function UserInfo() {
  const userInfo = useMicroAppState('userInfo')
  const permissions = useMicroAppState('permissions')
  
  return (
    <div>
      <h2>用户信息</h2>
      <p>姓名: {userInfo?.name || '未登录'}</p>
      <p>权限: {permissions?.join(', ') || '无权限'}</p>
    </div>
  )
}

// 订单列表组件
function OrderList() {
  const { sendMessage, broadcastMessage } = useMicroApp()
  const orders = useMicroAppState('orders')
  
  const notifyProductApp = () => {
    sendMessage('product-management', {
      action: 'syncInventory',
      orderIds: [1, 2, 3]
    })
  }
  
  const broadcastOrderUpdate = () => {
    broadcastMessage({
      type: 'orderUpdated',
      timestamp: Date.now()
    })
  }
  
  return (
    <div>
      <h2>订单列表</h2>
      <p>当前订单数: {orders?.length || 0}</p>
      <button onClick={notifyProductApp}>通知商品应用</button>
      <button onClick={broadcastOrderUpdate}>广播订单更新</button>
    </div>
  )
}

// 消息中心组件
function MessageCenter() {
  const { state, updateState } = useMicroApp()
  const messages = useMicroAppState('messages') || []
  
  const markAsRead = (messageId: string) => {
    const updatedMessages = messages.map((msg: any) => 
      msg.id === messageId ? { ...msg, isRead: true } : msg
    )
    updateState({ messages: updatedMessages })
  }
  
  return (
    <div>
      <h2>消息中心</h2>
      {messages.map((msg: any) => (
        <div key={msg.id} style={{ opacity: msg.isRead ? 0.5 : 1 }}>
          <p>{msg.content}</p>
          {!msg.isRead && (
            <button onClick={() => markAsRead(msg.id)}>标记已读</button>
          )}
        </div>
      ))}
    </div>
  )
}

export default OrderApp

// ============ 主应用使用示例 ============

function MainApp() {
  // 主应用不需要Provider，直接使用SDK
  const handleUserLogin = (userInfo: any) => {
    // 登录成功后，广播用户信息到所有子应用
    window.mainAppSDK.message.broadcast({
      userInfo,
      permissions: userInfo.permissions,
      loginTime: Date.now()
    })
  }
  
  const handleThemeChange = (theme: string) => {
    // 主题变化时，通知所有子应用
    window.mainAppSDK.message.broadcast({
      themeConfig: { mode: theme }
    })
  }
  
  return (
    <div>
      <h1>主应用</h1>
      <button onClick={() => handleUserLogin({ name: 'John', permissions: ['admin'] })}>
        模拟登录
      </button>
      <button onClick={() => handleThemeChange('dark')}>
        切换暗色主题
      </button>
      
      {/* 微前端容器 */}
      <div id="micro-app-container"></div>
    </div>
  )
} 