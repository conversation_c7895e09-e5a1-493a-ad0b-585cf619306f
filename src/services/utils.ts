import { cloneDeep } from 'lodash-es'

/**
 * 工具服务
 * 提供常用的工具函数
 */
const utilsService = {
  /**
   * 日期时间格式化
   */
  date: {
    /**
     * 格式化日期
     * @param date 日期对象或日期字符串或时间戳
     * @param format 格式化模式，默认：YYYY-MM-DD
     * @returns 格式化后的日期字符串
     */
    format: (date: string | number | Date, format = 'YYYY-MM-DD'): string => {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      const seconds = String(d.getSeconds()).padStart(2, '0')
      
      return format
        .replace('YYYY', year.toString())
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds)
    },

    /**
     * 获取当前日期时间
     * @returns 当前日期时间
     */
    now: (): Date => {
      return new Date()
    },

    /**
     * 计算两个日期之间的差值
     * @param date1 日期1
     * @param date2 日期2，默认为当前日期
     * @param unit 差值单位，可选：'second'|'minute'|'hour'|'day'|'month'|'year'
     * @returns 差值
     */
    diff: (
      date1: string | number | Date,
      date2: string | number | Date = new Date(),
      unit: 'second' | 'minute' | 'hour' | 'day' | 'month' | 'year' = 'day',
    ): number => {
      const d1 = new Date(date1).getTime()
      const d2 = new Date(date2).getTime()
      const diffMs = Math.abs(d1 - d2)
      
      switch (unit) {
        case 'second':
          return Math.floor(diffMs / 1000)
        case 'minute':
          return Math.floor(diffMs / (1000 * 60))
        case 'hour':
          return Math.floor(diffMs / (1000 * 60 * 60))
        case 'day':
          return Math.floor(diffMs / (1000 * 60 * 60 * 24))
        case 'month':
          return Math.floor(diffMs / (1000 * 60 * 60 * 24 * 30))
        case 'year':
          return Math.floor(diffMs / (1000 * 60 * 60 * 24 * 365))
        default:
          return Math.floor(diffMs / (1000 * 60 * 60 * 24))
      }
    },
  },

  /**
   * 字符串相关工具
   */
  string: {
    /**
     * 截取字符串，超出部分显示省略号
     * @param str 原字符串
     * @param length 截取长度
     * @returns 截取后的字符串
     */
    truncate: (str: string, length: number): string => {
      if (!str) return ''
      return str.length > length ? str.substr(0, length) + '...' : str
    },

    /**
     * 转换为大写
     * @param str 原字符串
     * @returns 转换后的字符串
     */
    toUpper: (str: string): string => {
      return str ? str.toUpperCase() : ''
    },

    /**
     * 转换为小写
     * @param str 原字符串
     * @returns 转换后的字符串
     */
    toLower: (str: string): string => {
      return str ? str.toLowerCase() : ''
    },
  },

  /**
   * 数组相关工具
   */
  array: {
    /**
     * 数组去重
     * @param arr 数组
     * @returns 去重后的数组
     */
    unique: <T>(arr: T[]): T[] => {
      if (!arr || !arr.length) return []
      return [...new Set(arr)]
    },

    /**
     * 查找数组中指定条件的项
     * @param arr 数组
     * @param predicate 查找条件
     * @returns 找到的项
     */
    find: <T>(arr: T[], predicate: (item: T, index: number) => boolean): T | undefined => {
      if (!arr || !arr.length) return undefined
      return arr.find(predicate)
    },

    /**
     * 过滤数组
     * @param arr 数组
     * @param predicate 过滤条件
     * @returns 过滤后的数组
     */
    filter: <T>(arr: T[], predicate: (item: T, index: number) => boolean): T[] => {
      if (!arr || !arr.length) return []
      return arr.filter(predicate)
    },
  },

  /**
   * 对象相关工具
   */
  object: {
    /**
     * 深拷贝对象
     * @param obj 对象
     * @returns 深拷贝后的对象
     */
    clone: <T>(obj: T): T => {
      return cloneDeep(obj)
    },

    /**
     * 获取对象指定路径的值
     * @param obj 对象
     * @param path 路径，如 'user.address.city'
     * @param defaultValue 默认值
     * @returns 找到的值或默认值
     */
    get: (obj: any, path: string, defaultValue?: any): any => {
      if (!obj || !path) return defaultValue
      const keys = path.split('.')
      let result = obj
      for (const key of keys) {
        result = result?.[key]
        if (result === undefined) return defaultValue
      }
      return result
    },
  },

  /**
   * URL相关工具
   */
  url: {
    /**
     * 解析URL参数
     * @param url URL字符串，默认为当前页面URL
     * @returns 参数对象
     */
    parseParams: (url: string = window.location.href): Record<string, string> => {
      const params: Record<string, string> = {}
      const urlObj = new URL(url)
      const searchParams = new URLSearchParams(urlObj.search)
      for (const [key, value] of searchParams.entries()) {
        params[key] = value
      }
      return params
    },

    /**
     * 构建URL
     * @param baseUrl 基础URL
     * @param params 参数对象
     * @returns 完整URL
     */
    buildUrl: (baseUrl: string, params: Record<string, any>): string => {
      if (!baseUrl) return ''
      const url = new URL(baseUrl)
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value))
        }
      })
      return url.toString()
    },
  },
}

export default utilsService
