import { getItem, setItem, removeItem } from '@/utils/localstorage'
import { StorageEnum } from '@/types/enum'

/**
 * 认证服务
 * 提供登录、登出和权限验证功能
 */
const authService = {
  /**
   * 获取当前用户token
   */
  getToken: () => {
    return getItem<string>(StorageEnum.Token)
  },

  /**
   * 设置用户token
   * @param token 用户token
   */
  setToken: (token: string) => {
    setItem(StorageEnum.Token, token)
  },

  /**
   * 清除token
   */
  clearToken: () => {
    removeItem(StorageEnum.Token)
  },

  /**
   * 登出
   */
  logout: () => {
    // 清除token
    authService.clearToken()
    // 清除用户信息
    removeItem(StorageEnum.User)
    // 重定向到登录页
    window.location.href = '/login'
  },

  /**
   * 检查用户是否已登录
   */
  isLoggedIn: () => {
    return !!authService.getToken()
  },

  /**
   * 检查用户是否有指定权限
   * @param permission 权限代码
   */
  hasPermission: (permission: string) => {
    const userInfo = getItem<any>(StorageEnum.User)
    return Array.isArray(userInfo?.permissions) ? userInfo.permissions.includes(permission) : false
  },

  /**
   * 检查用户是否有指定角色
   * @param role 角色代码
   */
  hasRole: (role: string) => {
    const userInfo = getItem<any>(StorageEnum.User)
    return Array.isArray(userInfo?.roles) ? userInfo.roles.includes(role) : false
  },
}

export default authService
