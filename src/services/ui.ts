// 简化的UI接口类型定义，不依赖antd
export interface MessageConfig {
  content: string;
  duration?: number;
  onClose?: () => void;
}

export interface NotificationConfig {
  message: string;
  description?: string;
  duration?: number;
  placement?: string;
}

export interface ModalConfig {
  title?: string;
  content: string;
  onOk?: () => void;
  onCancel?: () => void;
}

/**
 * UI服务
 * 提供消息提示、通知、弹窗等UI交互功能的接口定义
 * 注意：这是接口定义，实际UI组件由主应用提供
 */
const uiService = {
  /**
   * 显示成功消息
   * @param content 消息内容
   * @param duration 显示时长（秒）
   * @param onClose 关闭回调
   */
  success: (content: string, duration = 3, onClose?: () => void) => {
    console.log(`[UI] Success: ${content}`)
    // 实际实现应该由主应用注入
    if (window.mainAppSDK?.getService('ui')?.success) {
      return window.mainAppSDK.getService('ui').success(content, duration, onClose)
    }
  },

  /**
   * 显示错误消息
   * @param content 消息内容
   * @param duration 显示时长（秒）
   * @param onClose 关闭回调
   */
  error: (content: string, duration = 3, onClose?: () => void) => {
    console.log(`[UI] Error: ${content}`)
    if (window.mainAppSDK?.getService('ui')?.error) {
      return window.mainAppSDK.getService('ui').error(content, duration, onClose)
    }
  },

  /**
   * 显示警告消息
   * @param content 消息内容
   * @param duration 显示时长（秒）
   * @param onClose 关闭回调
   */
  warning: (content: string, duration = 3, onClose?: () => void) => {
    console.log(`[UI] Warning: ${content}`)
    if (window.mainAppSDK?.getService('ui')?.warning) {
      return window.mainAppSDK.getService('ui').warning(content, duration, onClose)
    }
  },

  /**
   * 显示信息消息
   * @param content 消息内容
   * @param duration 显示时长（秒）
   * @param onClose 关闭回调
   */
  info: (content: string, duration = 3, onClose?: () => void) => {
    console.log(`[UI] Info: ${content}`)
    if (window.mainAppSDK?.getService('ui')?.info) {
      return window.mainAppSDK.getService('ui').info(content, duration, onClose)
    }
  },

  /**
   * 显示加载中状态
   * @param content 消息内容
   * @param duration 显示时长（秒），为0时需要手动关闭
   */
  loading: (content: string, duration = 0) => {
    console.log(`[UI] Loading: ${content}`)
    if (window.mainAppSDK?.getService('ui')?.loading) {
      return window.mainAppSDK.getService('ui').loading(content, duration)
    }
  },

  /**
   * 显示成功通知
   * @param config 通知配置
   */
  notificationSuccess: (config: NotificationConfig) => {
    console.log(`[UI] Notification Success: ${config.message}`)
    if (window.mainAppSDK?.getService('ui')?.notificationSuccess) {
      return window.mainAppSDK.getService('ui').notificationSuccess(config)
    }
  },

  /**
   * 显示错误通知
   * @param config 通知配置
   */
  notificationError: (config: NotificationConfig) => {
    console.log(`[UI] Notification Error: ${config.message}`)
    if (window.mainAppSDK?.getService('ui')?.notificationError) {
      return window.mainAppSDK.getService('ui').notificationError(config)
    }
  },

  /**
   * 显示确认对话框
   * @param config 对话框配置
   */
  confirm: (config: ModalConfig) => {
    console.log(`[UI] Confirm: ${config.content}`)
    if (window.mainAppSDK?.getService('ui')?.confirm) {
      return window.mainAppSDK.getService('ui').confirm(config)
    }
    // 兜底实现
    return window.confirm(config.content)
  },

  /**
   * 显示信息对话框
   * @param config 对话框配置
   */
  modal: (config: ModalConfig) => {
    console.log(`[UI] Modal: ${config.content}`)
    if (window.mainAppSDK?.getService('ui')?.modal) {
      return window.mainAppSDK.getService('ui').modal(config)
    }
    // 兜底实现
    window.alert(config.content)
  },
}

export default uiService
