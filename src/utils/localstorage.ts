/**
 * localStorage 工具函数
 */

/**
 * 获取localStorage项
 */
export function getItem<T = any>(key: string): T | null {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : null
  } catch (error) {
    console.error(`Error getting localStorage item ${key}:`, error)
    return null
  }
}

/**
 * 设置localStorage项
 */
export function setItem(key: string, value: any): void {
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error setting localStorage item ${key}:`, error)
  }
}

/**
 * 移除localStorage项
 */
export function removeItem(key: string): void {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error(`Error removing localStorage item ${key}:`, error)
  }
}

/**
 * 清空localStorage
 */
export function clear(): void {
  try {
    localStorage.clear()
  } catch (error) {
    console.error('Error clearing localStorage:', error)
  }
} 