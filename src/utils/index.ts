/**
 * 工具函数集合
 */

/**
 * 解析 URL 参数
 * @param url URL 字符串
 */
export function getUrlParams(url: string): Record<string, string> {
  try {
    const params: Record<string, string> = {}
    const urlObj = new URL(url)
    const searchParams = new URLSearchParams(urlObj.search)

    searchParams.forEach((value, key) => {
      params[key] = value
    })

    return params
  } catch (e) {
    console.error('Error parsing URL:', e)
    return {}
  }
}

/**
 * 合并 URL 路径
 * @param baseURL 基础 URL
 * @param relativeURL 相对 URL
 */
export function combineURLs(baseURL: string, relativeURL: string): string {
  return relativeURL
    ? baseURL.replace(/\/+$/, '') + '/' + relativeURL.replace(/^\/+/, '')
    : baseURL
}

/**
 * 生成唯一 ID
 */
export function generateUniqueId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

/**
 * 节流函数
 * @param fn 要执行的函数
 * @param delay 延迟时间
 */
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let lastCall = 0
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall < delay) return
    lastCall = now
    return fn(...args)
  }
}

/**
 * 防抖函数
 * @param fn 要执行的函数
 * @param delay 延迟时间
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timer: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as any
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item)) as any
  }

  if (obj instanceof Object) {
    const copy: Record<string, any> = {}
    Object.keys(obj).forEach((key) => {
      copy[key] = deepClone((obj as Record<string, any>)[key])
    })
    return copy as T
  }

  return obj
}

/**
 * 格式化日期
 * @param date 日期对象或时间戳
 * @param format 格式化字符串
 */
export function formatDate(
  date: Date | string | number,
  format: string = 'YYYY-MM-DD HH:mm:ss',
): string {
  const d = new Date(date)

  const replacements: Record<string, string> = {
    YYYY: d.getFullYear().toString(),
    MM: (d.getMonth() + 1).toString().padStart(2, '0'),
    DD: d.getDate().toString().padStart(2, '0'),
    HH: d.getHours().toString().padStart(2, '0'),
    mm: d.getMinutes().toString().padStart(2, '0'),
    ss: d.getSeconds().toString().padStart(2, '0'),
  }

  return Object.entries(replacements).reduce(
    (result, [pattern, value]) => result.replace(pattern, value),
    format,
  )
}

/**
 * 检查是否处于生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}

/**
 * 检查是否处于开发环境
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

/**
 * 设置本地存储
 * @param key 键名
 * @param value 值
 */
export function setLocalStorage(key: string, value: any): void {
  try {
    const serializedValue = JSON.stringify(value)
    localStorage.setItem(key, serializedValue)
  } catch (e) {
    console.error('Error setting localStorage item:', e)
  }
}

/**
 * 获取本地存储
 * @param key 键名
 * @param defaultValue 默认值
 */
export function getLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    const serializedValue = localStorage.getItem(key)
    if (serializedValue === null) {
      return defaultValue
    }
    return JSON.parse(serializedValue)
  } catch (e) {
    console.error('Error getting localStorage item:', e)
    return defaultValue
  }
}

/**
 * 移除本地存储
 * @param key 键名
 */
export function removeLocalStorage(key: string): void {
  try {
    localStorage.removeItem(key)
  } catch (e) {
    console.error('Error removing localStorage item:', e)
  }
}

/**
 * 清空本地存储
 */
export function clearLocalStorage(): void {
  try {
    localStorage.clear()
  } catch (e) {
    console.error('Error clearing localStorage:', e)
  }
}
