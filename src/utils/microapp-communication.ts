/**
 * 微前端应用间通信工具
 */
export class MicroAppCommunication {
  private static globalData: Record<string, any> = {}
  private static appData: Record<string, any> = {}
  private static globalListeners: Array<(data: any) => void> = []
  private static appListeners: Record<string, Array<(data: any) => void>> = {}

  /**
   * 设置全局数据
   */
  static setGlobalData(data: any, callback?: (res: any[]) => void): void {
    this.globalData = { ...this.globalData, ...data }
    
    // 通知所有全局监听器
    this.globalListeners.forEach((listener) => {
      try {
        listener(data)
      } catch (e) {
        console.error('Error in global listener:', e)
      }
    })

    if (callback) {
      callback([])
    }
  }

  /**
   * 获取全局数据
   */
  static getGlobalData(): Record<string, any> {
    return this.globalData
  }

  /**
   * 设置应用数据
   */
  static setData(appName: string, data: any): void {
    this.appData[appName] = { ...this.appData[appName], ...data }
    
    // 通知对应应用的监听器
    const listeners = this.appListeners[appName] || []
    listeners.forEach((listener) => {
      try {
        listener(data)
      } catch (e) {
        console.error('Error in app listener:', e)
      }
    })
  }

  /**
   * 获取应用数据
   */
  static getData(appName: string): any {
    return this.appData[appName] || {}
  }

  /**
   * 添加全局数据监听器
   */
  static addGlobalDataListener(listener: (data: any) => void): void {
    this.globalListeners.push(listener)
  }

  /**
   * 移除全局数据监听器
   */
  static removeGlobalDataListener(listener: (data: any) => void): void {
    const index = this.globalListeners.indexOf(listener)
    if (index > -1) {
      this.globalListeners.splice(index, 1)
    }
  }

  /**
   * 添加应用数据监听器
   */
  static addDataListener(appName: string, listener: (data: any) => void, autoTrigger = false): void {
    if (!this.appListeners[appName]) {
      this.appListeners[appName] = []
    }
    this.appListeners[appName].push(listener)

    // 如果需要自动触发，立即调用一次
    if (autoTrigger && this.appData[appName]) {
      try {
        listener(this.appData[appName])
      } catch (e) {
        console.error('Error in auto-trigger listener:', e)
      }
    }
  }

  /**
   * 移除应用数据监听器
   */
  static removeDataListener(appName: string, listener: (data: any) => void): void {
    const listeners = this.appListeners[appName]
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }
} 