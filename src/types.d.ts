/**
 * 主应用SDK类型定义文件
 */

// 定义SDK接口
export interface MainAppSDKInterface {
  // 服务管理
  registerService(name: string, service: any): MainAppSDKInterface;
  getService(name: string): any;
  callMainMethod(serviceName: string, methodName: string, ...args: any[]): any;

  // 基础功能
  getUserInfo(): any;
  getRouterInfo(): any;
  navigateTo(path: string, options?: any): boolean;
  getThemeConfig(): any;

  // 模块定义
  message: {
    sendToApp(appName: string, data: any): void;
    broadcast(data: any, callback?: (res: any[]) => void): void;
    listenFromApp(
      appName: string,
      callback: (data: any) => void,
      autoTrigger?: boolean,
    ): () => void;
    listenGlobal(callback: (data: any) => void): void;
  };

  messageCenter: {
    getMessages(): any[];
    markAsRead(ids: any[]): Promise<boolean>;
    getJumpTypes(): any;
  };

  auth: {
    hasPermission(permission: string): boolean;
    getMenuPermissions(): any[];
  };
}

// 扩展全局window接口
declare global {
  interface Window {
    mainAppSDK: MainAppSDKInterface;
    __MICRO_APP_NAME__?: string;
    microApp?: any;
  }
}
