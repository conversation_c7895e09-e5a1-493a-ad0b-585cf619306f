# 主应用SDK使用文档

本文档介绍如何在子应用中使用主应用SDK提供的功能。

## 概述

主应用SDK（MainAppSDK）是由主应用提供的一套功能集合，子应用可以通过它来访问主应用的各种功能，包括但不限于：

- 用户认证与权限管理
- UI组件和交互
- 工具函数
- 消息通信
- 路由导航
- 共享组件

## 如何访问SDK

在子应用中，可以通过`window.mainAppSDK`来访问主应用SDK。

```typescript
// 获取SDK实例
const sdk = (window as any).mainAppSDK;

// 使用SDK功能
const userInfo = sdk.getUserInfo();
```

TypeScript类型定义：

```typescript
// 在子应用中添加类型定义
interface Window {
  mainAppSDK: any;
  microApp: any;
}
```

## 功能模块

### 基础功能

```typescript
// 获取用户信息
const userInfo = sdk.getUserInfo();

// 获取路由信息
const routerInfo = sdk.getRouterInfo();

// 导航到指定路径
sdk.navigateTo('/path/to/navigate', { 
  label: '页面标题',
  closable: true 
});

// 获取主题配置
const themeConfig = sdk.getThemeConfig();
```

### 服务调用

SDK提供了多个预注册的服务，可以通过以下方式调用：

```typescript
// 调用服务方法
sdk.callMainMethod('serviceName', 'methodName', ...args);

// 示例：调用认证服务的登出方法
sdk.callMainMethod('auth', 'logout');

// 示例：调用UI服务的消息提示
sdk.callMainMethod('ui', 'message.success', '操作成功');

// 示例：调用工具服务的日期格式化
const formattedDate = sdk.callMainMethod('utils', 'date.format', new Date(), 'YYYY-MM-DD HH:mm:ss');
```

### 共享组件

SDK提供了一套可以在子应用中直接使用的UI组件，这些组件继承了主应用的样式和交互逻辑，确保整体UI的一致性。

#### 获取组件

```typescript
// 获取指定组件
const CustomButton = sdk.getComponent('Button');

// 获取所有可用组件
const allComponents = sdk.getAllComponents();
```

#### 组件使用示例

在React子应用中使用共享组件：

```tsx
import React from 'react';
import { Form } from 'antd';

// 获取共享组件
const sdk = (window as any).mainAppSDK;
const { 
  Button, 
  Table, 
  Modal, 
  Card, 
  SearchForm, 
  PageContainer, 
  AdvancedSearch 
} = sdk.getAllComponents();

// 使用Form
const [form] = Form.useForm();

// 示例组件
const ExamplePage = () => {
  // 状态和逻辑...
  
  return (
    <PageContainer title="示例页面">
      {/* 使用高级搜索组件 */}
      <AdvancedSearch 
        form={form}
        fields={[
          {
            name: 'keyword',
            label: '关键字',
            component: <Input placeholder="请输入关键字" />
          },
          {
            name: 'status',
            label: '状态',
            component: <Select options={statusOptions} />
          }
        ]}
        onSearch={handleSearch}
      />
      
      {/* 使用表格组件 */}
      <Card title="数据列表" style={{ marginTop: 16 }}>
        <Button 
          type="primary" 
          onClick={handleAdd}
          style={{ marginBottom: 16 }}
        >
          新增
        </Button>
        
        <Table 
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          pagination={pagination}
        />
      </Card>
      
      {/* 使用弹窗组件 */}
      <Modal
        title="编辑信息"
        visible={visible}
        onCancel={handleCancel}
        onOk={handleOk}
      >
        {/* 弹窗内容 */}
      </Modal>
    </PageContainer>
  );
};
```

#### 可用组件列表

主应用提供的共享组件包括：

- `Button` - 按钮组件
- `Form` - 表单组件
- `Table` - 表格组件
- `Modal` - 弹窗组件
- `Card` - 卡片组件
- `SearchForm` - 搜索表单组件
- `PageContainer` - 页面容器组件
- `Tabs` - 选项卡组件
- `AdvancedSearch` - 高级搜索组件
- `AntdOriginal` - 原始Antd组件

#### 创建组件包装器

为了更好地使用共享组件，可以在子应用中创建组件包装器：

```tsx
// components/shared.tsx
import React from 'react';

// 获取共享组件
const sdk = (window as any).mainAppSDK;
const sharedComponents = sdk.getAllComponents();

// 导出所有组件
export const Button = sharedComponents.Button;
export const Table = sharedComponents.Table;
export const Modal = sharedComponents.Modal;
export const Card = sharedComponents.Card;
export const SearchForm = sharedComponents.SearchForm;
export const PageContainer = sharedComponents.PageContainer;
export const Tabs = sharedComponents.Tabs;
export const AdvancedSearch = sharedComponents.AdvancedSearch;

// 也可以导出原始Antd组件
export const AntdOriginal = sharedComponents.AntdOriginal;

// 默认导出
export default sharedComponents;
```

然后在应用中使用：

```tsx
import { Button, Table, PageContainer } from '@/components/shared';

const MyPage = () => {
  return (
    <PageContainer title="我的页面">
      <Button type="primary">点击我</Button>
      {/* 其他内容 */}
    </PageContainer>
  );
};
```

### 认证服务 (auth)

```typescript
// 获取token
const token = sdk.callMainMethod('auth', 'getToken');

// 设置token
sdk.callMainMethod('auth', 'setToken', 'your-token');

// 登出
sdk.callMainMethod('auth', 'logout');

// 检查登录状态
const isLoggedIn = sdk.callMainMethod('auth', 'isLoggedIn');

// 检查权限
const hasPermission = sdk.callMainMethod('auth', 'hasPermission', 'admin:system:view');

// 检查角色
const hasRole = sdk.callMainMethod('auth', 'hasRole', 'admin');
```

### UI服务 (ui)

```typescript
// 显示消息提示
sdk.callMainMethod('ui', 'message.success', '操作成功');
sdk.callMainMethod('ui', 'message.error', '操作失败');
sdk.callMainMethod('ui', 'message.warning', '警告信息');
sdk.callMainMethod('ui', 'message.info', '提示信息');

// 显示加载中消息
const loading = sdk.callMainMethod('ui', 'message.loading', '加载中...');
// 关闭加载提示
loading.destroy();

// 显示通知
sdk.callMainMethod('ui', 'notification.success', {
  message: '成功',
  description: '操作已成功完成'
});

// 显示确认对话框
sdk.callMainMethod('ui', 'modal.confirm', {
  title: '确认',
  content: '确定要执行此操作吗？',
  onOk: () => console.log('确认')
});
```

### 工具服务 (utils)

```typescript
// 日期格式化
const formattedDate = sdk.callMainMethod('utils', 'date.format', new Date(), 'YYYY-MM-DD');

// 获取当前时间
const now = sdk.callMainMethod('utils', 'date.now');

// 计算日期差
const daysDiff = sdk.callMainMethod('utils', 'date.diff', '2023-01-01', new Date(), 'day');

// 字符串截断
const truncated = sdk.callMainMethod('utils', 'string.truncate', '这是一段很长的文本', 8);

// 数组去重
const uniqueArray = sdk.callMainMethod('utils', 'array.unique', [1, 2, 2, 3, 3, 4]);

// 深拷贝对象
const cloned = sdk.callMainMethod('utils', 'object.clone', { a: 1, b: { c: 2 } });

// 解析URL参数
const params = sdk.callMainMethod('utils', 'url.parseParams', 'https://example.com?a=1&b=2');
```

### 消息通信

```typescript
// 发送消息到指定子应用
sdk.message.sendToApp('appName', { type: 'update', data: { id: 1 } });

// 发送全局消息
sdk.message.broadcast({ type: 'refresh' });

// 监听来自特定子应用的消息
const unsubscribe = sdk.message.listenFromApp('appName', (data) => {
  console.log('Received from app:', data);
});

// 取消监听
unsubscribe();

// 监听全局消息
sdk.message.listenGlobal((data) => {
  console.log('Global message:', data);
});

// 获取消息列表
const messages = sdk.messageCenter.getMessages();

// 标记消息为已读
sdk.messageCenter.markAsRead(['123', '456']);

// 获取跳转类型枚举
const jumpTypes = sdk.messageCenter.getJumpTypes();
```

### 权限管理

```typescript
// 检查权限
const hasPermission = sdk.auth.hasPermission('system:user:add');

// 获取菜单权限
const menuPermissions = sdk.auth.getMenuPermissions();
```

## 最佳实践

1. **在子应用初始化时检查SDK是否可用**

```typescript
function waitForSDK() {
  return new Promise((resolve) => {
    if ((window as any).mainAppSDK) {
      resolve((window as any).mainAppSDK);
      return;
    }
    
    const checkSDK = () => {
      if ((window as any).mainAppSDK) {
        resolve((window as any).mainAppSDK);
        window.removeEventListener('mainAppSDKReady', checkSDK);
      }
    };
    
    window.addEventListener('mainAppSDKReady', checkSDK);
    
    // 超时处理
    setTimeout(() => {
      if (!(window as any).mainAppSDK) {
        console.error('MainAppSDK not available after timeout');
        resolve(null);
      }
    }, 5000);
  });
}

// 使用
async function initApp() {
  const sdk = await waitForSDK();
  if (sdk) {
    // SDK可用，初始化应用
  } else {
    // SDK不可用，降级处理
  }
}
```

2. **创建SDK包装器**

在子应用中创建一个包装器，用于统一处理SDK调用：

```typescript
// sdk-wrapper.ts
class SDKWrapper {
  private sdk: any;
  
  constructor() {
    this.sdk = (window as any).mainAppSDK;
  }
  
  isAvailable() {
    return !!this.sdk;
  }
  
  // 获取共享组件
  getComponents() {
    return this.sdk.getAllComponents();
  }
  
  auth = {
    getUserInfo: () => this.sdk.getUserInfo(),
    hasPermission: (permission: string) => this.sdk.auth.hasPermission(permission),
    logout: () => this.sdk.callMainMethod('auth', 'logout')
  };
  
  ui = {
    showSuccess: (message: string) => this.sdk.callMainMethod('ui', 'message.success', message),
    showError: (message: string) => this.sdk.callMainMethod('ui', 'message.error', message),
    // 更多UI方法...
  };
  
  // 更多包装方法...
}

export const appSDK = new SDKWrapper();
```

## 注意事项

1. 始终检查SDK是否可用，以防主应用未正确加载SDK
2. 避免过度依赖主应用SDK，保持子应用的独立性
3. 处理好异常情况，当SDK功能不可用时提供降级方案
4. 在组件卸载时取消消息监听，防止内存泄漏
5. 使用共享组件时，注意组件属性和事件处理方式，确保正确传递props 

## 常见问题解决方案

### React Hooks 相关问题

#### 解决 "Invalid hook call" 错误

在微前端架构中，当子应用使用 React Hooks 时，可能会遇到 "Invalid hook call" 错误。这通常是由于多个 React 实例导致的，特别是当主应用和子应用使用不同版本的 React 时。

解决方案：

1. **使用主应用共享的 React**

   主应用已经将 React 和 ReactDOM 共享给子应用，子应用应该使用这些共享的库而不是自己安装的版本：

   ```jsx
   // 子应用入口文件中
   import { createRoot } from 'react-dom/client';
   
   // 使用主应用共享的 React
   const React = window.React;
   const ReactDOM = window.ReactDOM;
   
   // 挂载应用
   const root = ReactDOM.createRoot(document.getElementById('root'));
   root.render(<App />);
   ```

2. **配置 webpack 外部依赖**

   在子应用的 webpack 配置中，将 React 和 ReactDOM 设置为外部依赖：

   ```javascript
   // webpack.config.js
   module.exports = {
     // 其他配置...
     externals: {
       'react': 'React',
       'react-dom': 'ReactDOM',
       'react-dom/client': 'ReactDOM',
       'antd': 'Antd'
     }
   };
   ```

3. **确保子应用使用的 Hook 库与主应用一致**

   如果使用了其他依赖于 React 的库（如 react-router, redux 等），确保这些库也是使用同一个 React 实例。

4. **检查主应用 SDK 准备状态**

   在使用主应用提供的 React 和 ReactDOM 前，确保它们已加载完成：

   ```jsx
   // 子应用入口
   function renderApp() {
     const root = window.ReactDOM.createRoot(document.getElementById('root'));
     root.render(<App />);
   }
   
   // 检查 SDK 是否已就绪
   if (window.mainAppSDK) {
     renderApp();
   } else {
     // 监听 SDK 准备就绪事件
     document.addEventListener('mainAppSDKReady', renderApp);
   }
   ```

5. **使用主应用提供的 Antd 组件**

   如果使用 Antd 组件库，应该使用主应用共享的 Antd，而不是自己安装的版本：

   ```jsx
   // 不要这样使用
   // import { Button } from 'antd';
   
   // 而是这样使用
   const { Button } = window.Antd;
   
   // 或者从 SDK 中获取
   const Button = window.mainAppSDK.getComponent('Button');
   ```

通过以上措施，可以确保子应用和主应用使用相同的 React 实例，从而避免 "Invalid hook call" 错误。 

#### Vite 子应用特殊配置

对于使用 Vite 构建的子应用，需要进行一些特殊配置来解决 React Hooks 相关问题：

1. **配置 Vite 外部依赖**

   在 Vite 子应用的 `vite.config.ts` 或 `vite.config.js` 中配置外部依赖：

   ```typescript
   // vite.config.ts
   import { defineConfig } from 'vite';
   import react from '@vitejs/plugin-react';

   export default defineConfig({
     plugins: [react()],
     // 配置外部依赖
     build: {
       rollupOptions: {
         external: ['react', 'react-dom', 'antd'],
         output: {
           globals: {
             'react': 'React',
             'react-dom': 'ReactDOM',
             'antd': 'Antd'
           }
         }
       }
     }
   });
   ```

2. **调整 Vite 入口文件**

   修改 Vite 子应用入口文件（如 `main.tsx` 或 `main.jsx`）：

   ```tsx
   // main.tsx

   // 不要使用这种方式直接导入
   // import React from 'react';
   // import ReactDOM from 'react-dom/client';
   // import { createRoot } from 'react-dom/client';

   // 而是使用主应用共享的 React
   const React = (window as any).React;
   const ReactDOM = (window as any).ReactDOM;

   import App from './App';

   function renderApp() {
     const rootElement = document.getElementById('root');
     if (rootElement) {
       const root = ReactDOM.createRoot(rootElement);
       root.render(React.createElement(App));
     }
   }

   // 检查 SDK 是否可用
   if ((window as any).mainAppSDK) {
     renderApp();
   } else {
     document.addEventListener('mainAppSDKReady', renderApp);
   }
   ```

3. **使用别名替换直接导入（可选但推荐）**

   通过配置 Vite 的 `resolve.alias` 来拦截 React 导入：

   ```typescript
   // vite.config.ts
   import { defineConfig } from 'vite';
   import react from '@vitejs/plugin-react';
   import path from 'path';

   export default defineConfig({
     plugins: [react()],
     resolve: {
       alias: {
         'react': path.resolve(__dirname, './src/react-external.js'),
         'react-dom': path.resolve(__dirname, './src/react-dom-external.js'),
         'react-dom/client': path.resolve(__dirname, './src/react-dom-external.js'),
       }
     },
     // 其他配置...
   });
   ```

   然后创建这些别名文件：

   ```javascript
   // src/react-external.js
   export default window.React;
   export const { useState, useEffect, useRef, useMemo, useCallback, useContext, createContext } = window.React;
   ```

   ```javascript
   // src/react-dom-external.js
   export default window.ReactDOM;
   export const { createRoot, createPortal } = window.ReactDOM;
   ```

4. **应用加载时序配置**

   在 Vite 子应用中，确保在主应用 SDK 准备好后再渲染：

   ```typescript
   // src/micro-app-entry.ts 或在主入口文件中
   // 提供一个明确的加载入口点
   
   async function waitForMainAppSDK(timeout = 5000) {
     if ((window as any).mainAppSDK) {
       return (window as any).mainAppSDK;
     }
     
     return new Promise((resolve, reject) => {
       const timer = setTimeout(() => {
         reject(new Error('MainApp SDK timeout'));
       }, timeout);
       
       const handler = (event: CustomEvent) => {
         clearTimeout(timer);
         resolve(event.detail?.sdk || (window as any).mainAppSDK);
         document.removeEventListener('mainAppSDKReady', handler as EventListener);
       };
       
       document.addEventListener('mainAppSDKReady', handler as EventListener);
     });
   }
   
   export async function bootstrap() {
     try {
       await waitForMainAppSDK();
       // 应用初始化逻辑...
     } catch (error) {
       console.error('Failed to get MainApp SDK:', error);
       // 降级处理...
     }
   }
   ```

通过以上特定于 Vite 的配置，可以确保 Vite 构建的子应用也能正确共享 React 实例，避免 "Invalid hook call" 错误。 