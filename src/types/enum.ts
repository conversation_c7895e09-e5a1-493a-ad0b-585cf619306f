/**
 * 存储相关枚举
 */
export enum StorageEnum {
  USER_TOKEN = 'user_token',
  USER_INFO = 'user_info',
  THEME_CONFIG = 'theme_config',
  LANGUAGE = 'language',
  MENU_COLLAPSED = 'menu_collapsed',
  // 兼容之前的命名
  Token = 'user_token',
  User = 'user_info',
}

/**
 * API相关枚举
 */
export enum ApiEnum {
  SUCCESS = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_SERVER_ERROR = 500,
}

/**
 * 主题枚举
 */
export enum ThemeEnum {
  LIGHT = 'light',
  DARK = 'dark',
}

/**
 * 语言枚举
 */
export enum LanguageEnum {
  ZH_CN = 'zh-CN',
  EN_US = 'en-US',
} 