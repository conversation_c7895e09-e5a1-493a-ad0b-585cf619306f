/**
 * 通信适配器相关类型定义
 */

import type { SDKConfig } from './index'

// 通用数据类型
export type CommunicationData = Record<string, unknown>

// 数据监听器回调类型
export type CommunicationCallback = (data: CommunicationData) => void

// 广播响应类型
export type BroadcastResponse = Record<string, unknown>

// 通信适配器接口
export interface CommunicationAdapter {
  init(config: SDKConfig): void;
  sendData(appName: string, data: CommunicationData): void;
  listenData(appName: string, callback: CommunicationCallback): () => void;
  broadcast(data: CommunicationData, callback?: (res: BroadcastResponse[]) => void): void;
  getGlobalData(): CommunicationData | undefined;
  destroy(): void;
}

// MicroApp 通信适配器
export interface MicroAppCommunicationAdapter extends CommunicationAdapter {
  setGlobalData(data: CommunicationData): void;
  addGlobalDataListener(callback: CommunicationCallback): void;
  removeGlobalDataListener(callback: CommunicationCallback): void;
  clearDataListener(appName: string): void;
}

// PostMessage 通信适配器
export interface PostMessageCommunicationAdapter extends CommunicationAdapter {
  targetOrigin: string;
  messagePrefix: string;
}

// 自定义通信适配器
export interface CustomCommunicationAdapter extends CommunicationAdapter {
  // 允许自定义实现扩展
  [key: string]: unknown;
}
