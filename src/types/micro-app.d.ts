/**
 * @micro-zoe/micro-app 相关类型定义
 */

export interface MicroApp {
  dispatch: (data: any) => void
  addDataListener: (callback: (data: any) => void, autoTrigger?: boolean) => void
  removeDataListener: (callback: (data: any) => void) => void
  clearDataListener: () => void
  getData: () => any
}

declare global {
  interface Window {
    microApp?: MicroApp
    __MICRO_APP_NAME__?: string
    __MICRO_APP_ENVIRONMENT__?: string
  }
} 