/**
 * @micro-zoe/micro-app 相关类型定义
 * 基于 @micro-zoe/micro-app v1.0.0-rc.26
 */

// 通用数据类型
export type MicroAppData = Record<string, unknown>

// 数据监听器回调函数类型
export type DataListener = (data: MicroAppData) => void

// 路由位置信息类型
export interface RouteLocation {
  pathname: string
  search: string
  hash: string
  state?: unknown
  fullPath: string
}

// 子应用中的 microApp 对象类型
export interface MicroApp {
  // 数据通信
  dispatch: (data: MicroAppData) => void
  getData: () => MicroAppData | undefined
  setGlobalData: (data: MicroAppData) => void
  getGlobalData: () => MicroAppData | undefined

  // 监听器管理
  addDataListener: (callback: DataListener, autoTrigger?: boolean) => void
  removeDataListener: (callback: DataListener) => void
  clearDataListener: () => void
  addGlobalDataListener: (callback: DataListener, autoTrigger?: boolean) => void
  removeGlobalDataListener: (callback: DataListener) => void
  clearGlobalDataListener: () => void

  // DOM 操作
  removeDomScope: (force?: boolean) => void
  pureCreateElement: (tagName: string) => Element

  // 路由相关
  router: {
    encode: (path: string) => string
    decode: (path: string) => string
    current: {
      get: (appName: string) => RouteLocation | undefined
    }
  }
}

// 主应用中的 microApp 对象类型
export interface MicroAppMain {
  // 初始化
  start: (options?: MicroAppStartOptions) => void

  // 数据通信
  setData: (appName: string, data: MicroAppData) => void
  getData: (appName: string) => MicroAppData | undefined
  setGlobalData: (data: MicroAppData) => void
  getGlobalData: () => MicroAppData | undefined

  // 监听器管理
  addDataListener: (appName: string, callback: DataListener, autoTrigger?: boolean) => void
  removeDataListener: (appName: string, callback: DataListener) => void
  clearDataListener: (appName: string) => void
  addGlobalDataListener: (callback: DataListener, autoTrigger?: boolean) => void
  removeGlobalDataListener: (callback: DataListener) => void

  // 应用管理
  renderApp: (options: RenderAppOptions) => Promise<boolean>

  // 路由
  router: MicroAppRouter
}

// 生命周期事件类型
export interface LifeCycleEvent extends CustomEvent {
  detail: {
    name: string
    url: string
    container: Element
  }
}

// 生命周期回调类型
export type LifeCycleCallback = (event: LifeCycleEvent, appName: string) => void

// 插件选项类型
export interface PluginOptions {
  scopeProperties?: PropertyKey[]
  escapeProperties?: PropertyKey[]
  excludeChecker?: (url: string) => boolean
  ignoreChecker?: (url: string) => boolean
  options?: Record<string, unknown>
  loader?: (code: string, url: string, options?: Record<string, unknown>) => string
  processHtml?: (code: string, url: string, options?: Record<string, unknown>) => string
}

// 预取应用配置
export interface PreFetchApp {
  name: string
  url: string
  'disable-scopecss'?: boolean
  'disable-sandbox'?: boolean
  inline?: boolean
  iframe?: boolean
  level?: number
}

// 启动选项
export interface MicroAppStartOptions {
  tagName?: string
  iframe?: boolean
  inline?: boolean
  destroy?: boolean
  ssr?: boolean
  'disable-scopecss'?: boolean
  'disable-sandbox'?: boolean
  'keep-alive'?: boolean
  'disable-memory-router'?: boolean
  'keep-router-state'?: boolean
  'disable-patch-request'?: boolean
  'router-mode'?: string
  iframeSrc?: string
  lifeCycles?: {
    created?: LifeCycleCallback
    beforemount?: LifeCycleCallback
    mounted?: LifeCycleCallback
    unmount?: LifeCycleCallback
    error?: LifeCycleCallback
    beforeshow?: LifeCycleCallback
    aftershow?: LifeCycleCallback
    afterhidden?: LifeCycleCallback
  }
  preFetchApps?: PreFetchApp[]
  plugins?: {
    global?: PluginOptions[]
    modules?: {
      [appName: string]: PluginOptions[]
    }
  }
  fetch?: (url: string, options: Record<string, unknown>, appName: string | null) => Promise<string>
  globalAssets?: {
    js?: string[]
    css?: string[]
  }
  excludeAssetFilter?: (assetUrl: string) => boolean
}

// 路由目标类型
export interface RouterTarget {
  name: string
  path: string
  state?: unknown
  replace?: boolean
}

// 路由守卫类型
export type RouterGuard = (to: RouteLocation, from: RouteLocation, appName?: string) => void | Promise<void>

// 渲染应用选项
export interface RenderAppOptions {
  name: string
  url: string
  container: string | Element
  iframe?: boolean
  inline?: boolean
  'disable-scopecss'?: boolean
  'disable-sandbox'?: boolean
  'disable-memory-router'?: boolean
  'default-page'?: string
  'keep-router-state'?: boolean
  'disable-patch-request'?: boolean
  'keep-alive'?: boolean
  destroy?: boolean
  fiber?: boolean
  baseroute?: string
  ssr?: boolean
  data?: MicroAppData
  onDataChange?: DataListener
  lifeCycles?: {
    created?: LifeCycleCallback
    beforemount?: LifeCycleCallback
    mounted?: LifeCycleCallback
    unmount?: LifeCycleCallback
    error?: LifeCycleCallback
    beforeshow?: LifeCycleCallback
    aftershow?: LifeCycleCallback
    afterhidden?: LifeCycleCallback
  }
}

// 路由相关类型
export interface MicroAppRouter {
  current: Map<string, RouteLocation>
  encode: (path: string) => string
  decode: (path: string) => string
  push: (to: RouterTarget) => Promise<void>
  replace: (to: RouterTarget) => Promise<void>
  go: (delta: number) => void
  back: () => void
  forward: () => void
  beforeEach: (guard: RouterGuard) => () => boolean
  afterEach: (guard: RouterGuard) => () => boolean
  setDefaultPage: (options: { name: string; path: string }) => () => boolean
  removeDefaultPage: (appName: string) => boolean
  getDefaultPage: (appName: string) => string | undefined
  attachToURL: (appName: string) => void
  attachAllToURL: (options?: { includeHiddenApp?: boolean; includePreRender?: boolean }) => void
}

declare global {
  interface Window {
    microApp?: MicroApp
    __MICRO_APP_NAME__?: string
    __MICRO_APP_ENVIRONMENT__?: boolean
  }
}