/**
 * 微前端Web SDK类型定义文件
 */

// SDK配置接口
export interface SDKConfig {
  appName: string;
  version: string;
  communicationMode: 'microApp' | 'postMessage' | 'custom';
  debug?: boolean;
  customCommunicator?: any;
}

// 导航选项
export interface NavigationOptions {
  label?: string;
  closable?: boolean;
  query?: Record<string, any>;
  [key: string]: any;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 请求选项
export interface RequestOptions extends RequestInit {
  params?: Record<string, any>;
  timeout?: number;
  url?: string;
}

// 服务定义
export interface ServiceDefinition {
  name: string;
  methods: Record<string, (...args: any[]) => Promise<any>>;
}

// 消息模块接口
export interface MessageModule {
  sendToApp(appName: string, data: any): void;
  broadcast(data: any, callback?: (res: any[]) => void): void;
  listenFromApp(
    appName: string,
    callback: (data: any) => void,
    autoTrigger?: boolean,
  ): () => void;
  listenGlobal(callback: (data: any) => void): void;
}

// 消息中心模块接口
export interface MessageCenterModule {
  getMessages(): any[];
  markAsRead(ids: any[]): Promise<boolean>;
  getJumpTypes(): any;
}

// 权限模块接口
export interface AuthModule {
  hasPermission(permission: string): boolean;
  getMenuPermissions(): any[];
}

// SDK主接口
export interface MicroWebSDKInterface {
  // 核心配置
  config: SDKConfig;
  
  // 服务管理
  registerService(name: string, service: any): MicroWebSDKInterface;
  getService(name: string): any;
  callMainMethod(serviceName: string, methodName: string, ...args: any[]): any;

  // 组件管理
  registerComponent(name: string, component: any): MicroWebSDKInterface;
  getComponent(name: string): any;
  getAllComponents(): Record<string, any>;

  // 基础功能
  getUserInfo(): any;
  getRouterInfo(): any;
  navigateTo(path: string, options?: NavigationOptions): boolean;
  getThemeConfig(): any;

  // 功能模块
  message: MessageModule;
  messageCenter: MessageCenterModule;
  auth: AuthModule;

  // 工具方法
  destroy(): void;
  getVersion(): string;
}

// 通信适配器接口
export interface CommunicationAdapter {
  init(config: SDKConfig): void;
  sendData(appName: string, data: any): void;
  listenData(appName: string, callback: (data: any) => void): () => void;
  broadcast(data: any, callback?: (res: any[]) => void): void;
  getGlobalData(): any;
  destroy(): void;
}

// 扩展全局window接口
declare global {
  interface Window {
    microWebSDK?: MicroWebSDKInterface;
    __MICRO_APP_NAME__?: string;
    __MICRO_APP_ENVIRONMENT__?: boolean;
    microApp?: any;
  }
}

// 默认导出所有类型
export * from './index'