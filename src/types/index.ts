/**
 * 微前端Web SDK类型定义文件
 */

import type { CommunicationAdapter } from './communication.js'

// SDK配置接口
export interface SDKConfig {
  appName: string;
  version: string;
  communicationMode: 'microApp' | 'postMessage' | 'custom';
  debug?: boolean;
  customCommunicator?: CommunicationAdapter;
}

// 导航选项
export interface NavigationOptions {
  label?: string;
  closable?: boolean;
  query?: Record<string, unknown>;
  [key: string]: unknown;
}

// API响应接口
export interface ApiResponse<T = unknown> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 请求选项
export interface RequestOptions extends RequestInit {
  params?: Record<string, unknown>;
  timeout?: number;
  url?: string;
}

// 服务方法类型
export type ServiceMethod = (...args: unknown[]) => Promise<unknown>

// 服务定义
export interface ServiceDefinition {
  name: string;
  methods: Record<string, ServiceMethod>;
}

// 消息数据类型
export type MessageData = Record<string, unknown>

// 消息回调类型
export type MessageCallback = (data: MessageData) => void

// 消息响应类型
export type MessageResponse = Record<string, unknown>

// 消息对象类型
export interface Message {
  id: string | number
  title: string
  content: string
  type: 'info' | 'warning' | 'error' | 'success'
  read: boolean
  timestamp: number
  [key: string]: unknown
}

// 跳转类型配置
export interface JumpType {
  type: string
  label: string
  handler: (data: unknown) => void
}

// 菜单权限类型
export interface MenuPermission {
  id: string
  name: string
  path: string
  children?: MenuPermission[]
  [key: string]: unknown
}

// 消息模块接口
export interface MessageModule {
  sendToApp(appName: string, data: MessageData): void;
  broadcast(data: MessageData, callback?: (res: MessageResponse[]) => void): void;
  listenFromApp(
    appName: string,
    callback: MessageCallback,
    autoTrigger?: boolean,
  ): () => void;
  listenGlobal(callback: MessageCallback): void;
}

// 消息中心模块接口
export interface MessageCenterModule {
  getMessages(): Message[];
  markAsRead(ids: Array<string | number>): Promise<boolean>;
  getJumpTypes(): JumpType[];
}

// 权限模块接口
export interface AuthModule {
  hasPermission(permission: string): boolean;
  getMenuPermissions(): MenuPermission[];
}

// 用户信息类型
export interface UserInfo {
  id: string | number
  username: string
  email?: string
  avatar?: string
  roles?: string[]
  permissions?: string[]
  [key: string]: unknown
}

// 路由信息类型
export interface RouterInfo {
  path: string
  query: Record<string, unknown>
  params: Record<string, unknown>
  hash: string
  fullPath: string
  [key: string]: unknown
}

// 主题配置类型
export interface ThemeConfig {
  primaryColor: string
  mode: 'light' | 'dark'
  [key: string]: unknown
}

// 组件类型
export type ComponentType = React.ComponentType<unknown> | unknown

// SDK主接口
export interface MicroWebSDKInterface {
  // 核心配置
  config: SDKConfig;

  // 服务管理
  registerService(name: string, service: ServiceDefinition): MicroWebSDKInterface;
  getService(name: string): ServiceDefinition | undefined;
  callMainMethod(serviceName: string, methodName: string, ...args: unknown[]): unknown;

  // 组件管理
  registerComponent(name: string, component: ComponentType): MicroWebSDKInterface;
  getComponent(name: string): ComponentType | undefined;
  getAllComponents(): Record<string, ComponentType>;

  // 基础功能
  getUserInfo(): UserInfo | undefined;
  getRouterInfo(): RouterInfo | undefined;
  navigateTo(path: string, options?: NavigationOptions): boolean;
  getThemeConfig(): ThemeConfig | undefined;

  // 功能模块
  message: MessageModule;
  messageCenter: MessageCenterModule;
  auth: AuthModule;

  // 工具方法
  destroy(): void;
  getVersion(): string;
}

// 扩展全局window接口
declare global {
  interface Window {
    microWebSDK?: MicroWebSDKInterface;
    __MICRO_APP_NAME__?: string;
    __MICRO_APP_ENVIRONMENT__?: boolean;
  }
}

// 重新导出相关类型
export type { MicroApp, MicroAppMain, MicroAppData, DataListener } from './micro-app'
export type { CommunicationAdapter } from './communication.js'