/**
 * WebSocket 相关的类型定义和枚举
 */

/**
 * 跳转类型枚举
 */
export enum JumpType {
  // 内部页面跳转
  INTERNAL = 'internal',
  // 外部链接跳转
  EXTERNAL = 'external',
  // 弹窗方式打开
  MODAL = 'modal',
  // 新标签页打开
  NEW_TAB = 'new_tab',
  // 替换当前页面
  REPLACE = 'replace',
}

/**
 * 消息类型
 */
export enum MessageType {
  SYSTEM = 'system',
  NOTICE = 'notice',
  WARNING = 'warning',
  ERROR = 'error',
}

/**
 * WebSocket连接状态
 */
export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
}

/**
 * 消息结构接口
 */
export interface MessageItem {
  msgId: string;
  msgType: MessageType;
  title: string;
  content: string;
  isRead: boolean;
  timestamp: number;
  jumpType?: JumpType;
  jumpUrl?: string;
}

/**
 * WebSocket信息Hook (模拟实现)
 */
export function useWebSocketInfo() {
  return {
    status: WebSocketStatus.DISCONNECTED,
    connect: () => {},
    disconnect: () => {},
    send: (message: any) => {},
  }
} 