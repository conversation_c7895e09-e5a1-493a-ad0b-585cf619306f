/**
 * 路由状态管理
 */
interface RouterState {
  currentPath: string;
  asyncRouters: any[];
  history: string[];
}

class RouterStore {
  private state: RouterState = {
    currentPath: '/',
    asyncRouters: [],
    history: [],
  }

  getState(): RouterState {
    return { ...this.state }
  }

  setState(newState: Partial<RouterState>): void {
    this.state = { ...this.state, ...newState }
  }

  setCurrentPath(path: string): void {
    if (path !== this.state.currentPath) {
      this.state.history.push(this.state.currentPath)
      this.state.currentPath = path
    }
  }

  setAsyncRouters(routers: any[]): void {
    this.state.asyncRouters = routers
  }
}

const useRouterStore = new RouterStore()

export default useRouterStore 