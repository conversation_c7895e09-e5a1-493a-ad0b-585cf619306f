/**
 * Socket 状态管理
 */
interface SocketState {
  socketStoreMap: Map<string, any[]>;
  actions: {
    getAllValues(): any[];
    getItem(key: string): any[];
    setItem(key: string, value: any[]): void;
  };
}

class SocketStore {
  private state: SocketState = {
    socketStoreMap: new Map(),
    actions: {
      getAllValues: () => {
        const allValues: any[] = []
        this.state.socketStoreMap.forEach((values) => {
          allValues.push(...values)
        })
        return allValues
      },
      getItem: (key: string) => {
        return this.state.socketStoreMap.get(key) || []
      },
      setItem: (key: string, value: any[]) => {
        this.state.socketStoreMap.set(key, value)
      },
    },
  }

  getState(): SocketState {
    return this.state
  }

  setState(updater: (state: SocketState) => Partial<SocketState>): void {
    const updates = updater(this.state)
    this.state = { ...this.state, ...updates }
  }
}

const socketStoreInstance = new SocketStore()

export const useSocketStore = {
  getState: () => socketStoreInstance.getState(),
  setState: (updater: (state: SocketState) => Partial<SocketState>) => 
    socketStoreInstance.setState(updater),
} 