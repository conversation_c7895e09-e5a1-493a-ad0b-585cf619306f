import { MicroAppCommunication } from '@/utils/microapp-communication'
import useRouterStore from '@/store/router-store'
import { JumpType } from '@/hooks/use-web-socket-info'
import { cloneDeep } from 'lodash-es'
import { useSocketStore } from '@/store/socket-store'
import { MainAppSDKInterface } from './types'

// 定义基础类型
type IObject = Record<string, any>

/**
 * 主应用SDK
 * 为子应用提供统一的接口调用主应用的功能
 */
class MainAppSDK implements MainAppSDKInterface {
  // 存储所有共享服务
  private _services: Record<string, any> = {}

  // 注册服务
  registerService(name: string, service: any) {
    this._services[name] = service
    return this
  }

  // 获取服务
  getService(name: string) {
    return this._services[name]
  }

  // 调用主应用方法
  callMainMethod(serviceName: string, methodName: string, ...args: any[]) {
    const service = this.getService(serviceName)
    if (!service || typeof service[methodName] !== 'function') {
      console.error(`Method ${methodName} not found in service ${serviceName}`)
      return null
    }
    return service[methodName](...args)
  }

  // 获取用户信息
  getUserInfo() {
    return MicroAppCommunication.getGlobalData()?.userInfo || {}
  }

  // 获取当前路由信息
  getRouterInfo() {
    return useRouterStore.getState()
  }

  // 路由跳转 - 使用window.history
  navigateTo(
    path: string,
    options: { label?: string; closable?: boolean; [key: string]: any } = {},
  ) {
    // 处理带查询参数的URL
    let url = path
    const query = options.query || {}
    if (Object.keys(query).length > 0) {
      const searchParams = new URLSearchParams()
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      const queryString = searchParams.toString()
      if (queryString) {
        url = `${path}?${queryString}`
      }
    }

    // 存储标签页信息供后续使用
    if (options.label) {
      try {
        const tabsInfo = JSON.parse(localStorage.getItem('tabs_info') || '{}')
        tabsInfo[path] = {
          label: options.label,
          closable: options.closable !== false,
        }
        localStorage.setItem('tabs_info', JSON.stringify(tabsInfo))
      } catch (e) {
        console.error('存储标签信息失败:', e)
      }
    }

    // 执行导航
    try {
      window.location.href = url
      return true
    } catch (e) {
      console.error('导航失败:', e)
      return false
    }
  }

  // 获取主题配置
  getThemeConfig() {
    return MicroAppCommunication.getGlobalData()?.themeConfig || {}
  }

  // 消息通知相关方法
  message = {
    // 发送消息到指定子应用
    sendToApp: (appName: string, data: any) => {
      MicroAppCommunication.setData(appName, data)
    },

    // 发送全局消息
    broadcast: (data: any, callback?: (res: any[]) => void) => {
      MicroAppCommunication.setGlobalData(data, callback)
    },

    // 监听来自特定子应用的消息
    listenFromApp: (appName: string, callback: (data: any) => void, autoTrigger = false) => {
      MicroAppCommunication.addDataListener(appName, callback, autoTrigger)

      // 返回销毁函数，方便卸载监听
      return () => {
        MicroAppCommunication.removeDataListener(appName, callback)
      }
    },

    // 监听全局消息
    listenGlobal: (callback: (data: any) => void) => {
      MicroAppCommunication.addGlobalDataListener(callback)
    },
  }

  // 消息中心功能封装
  messageCenter = {
    // 获取消息列表
    getMessages: () => {
      const { actions } = useSocketStore.getState()
      return actions?.getAllValues() || []
    },

    // 标记消息为已读
    markAsRead: async (ids: any[] = []) => {
      // 因为useMessageCenter是一个React Hook，不能在这里直接调用
      // 所以我们直接使用socketStore来操作
      // 获取socketStore中的所有消息
      const list = useSocketStore.getState().actions.getAllValues()

      // 更新消息状态
      ids.forEach((id: any) => {
        const item = list.find((item: any) => item.msgId === id)
        if (item) {
          const key = item.msgType
          let msgList = cloneDeep(useSocketStore.getState().actions.getItem(key))
          msgList = msgList?.map((msg: any) => {
            if (msg.msgId === item.msgId) {
              return { ...msg, isRead: true }
            }
            return msg
          })

          const socketStoreMap = new Map(useSocketStore.getState().socketStoreMap)
          socketStoreMap.set(key, msgList)
          useSocketStore.setState(() => ({ socketStoreMap }))
        }
      })
      return true
    },

    // 获取跳转类型枚举
    getJumpTypes: () => {
      return JumpType
    },
  }

  // 用户权限相关
  auth = {
    // 检查权限
    hasPermission: (permission: string) => {
      const userInfo = this.getUserInfo() as IObject
      // 使用可选链和类型转换防止错误
      return Array.isArray(userInfo?.permissions)
        ? userInfo.permissions.includes(permission)
        : false
    },

    // 获取当前用户菜单权限
    getMenuPermissions: () => {
      const { asyncRouters } = useRouterStore.getState()
      return asyncRouters || []
    },
  }
}

// 创建单例实例
export const mainAppSDK: MainAppSDKInterface = new MainAppSDK()

// 默认导出实例
export default mainAppSDK

// 导出React相关功能
export { 
  MicroAppProvider, 
  useMicroApp, 
  useMicroAppState, 
  microAppManager,
} from './core'
