/**
 * 服务相关功能
 */

// 基础请求配置
interface RequestOptions extends RequestInit {
  params?: Record<string, any>;
  timeout?: number;
  url?: string;
}

// 响应接口
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 服务注册表
type ServiceRegistry = Record<string, ServiceDefinition>

// 服务定义
interface ServiceDefinition {
  name: string;
  methods: Record<string, (...args: any[]) => Promise<any>>;
}

// 请求拦截器
let requestInterceptor: ((config: RequestOptions) => RequestOptions) | null = null

// 响应拦截器
let responseInterceptor: ((response: any) => any) | null = null

// 错误拦截器
let errorInterceptor: ((error: any) => any) | null = null

// 服务注册表
const serviceRegistry: ServiceRegistry = {}

/**
 * 设置请求拦截器
 * @param interceptor 拦截器函数
 */
export function setRequestInterceptor(
  interceptor: (config: RequestOptions) => RequestOptions,
): void {
  requestInterceptor = interceptor
}

/**
 * 设置响应拦截器
 * @param interceptor 拦截器函数
 */
export function setResponseInterceptor(interceptor: (response: any) => any): void {
  responseInterceptor = interceptor
}

/**
 * 设置错误拦截器
 * @param interceptor 拦截器函数
 */
export function setErrorInterceptor(interceptor: (error: any) => any): void {
  errorInterceptor = interceptor
}

/**
 * 构建请求 URL 和参数
 * @param url 请求 URL
 * @param options 请求选项
 */
function buildRequest(
  url: string,
  options: RequestOptions = {},
): { url: string; options: RequestOptions } {
  let finalUrl = url
  const finalOptions = { ...options }

  // 处理查询参数
  if (options.params) {
    const queryParams = new URLSearchParams()
    for (const key in options.params) {
      if (Object.prototype.hasOwnProperty.call(options.params, key)) {
        const value = options.params[key]
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      }
    }

    const queryString = queryParams.toString()
    if (queryString) {
      finalUrl += (finalUrl.includes('?') ? '&' : '?') + queryString
    }

    // 移除 params 属性，因为它不是标准的 RequestInit 属性
    delete finalOptions.params
  }

  // 添加默认 headers
  finalOptions.headers = {
    'Content-Type': 'application/json',
    ...finalOptions.headers,
  }

  // 应用请求拦截器
  if (requestInterceptor) {
    finalOptions.url = finalUrl
    const intercepted = requestInterceptor(finalOptions)
    return {
      url: intercepted.url || finalUrl,
      options: { ...intercepted, url: undefined },
    }
  }

  return { url: finalUrl, options: finalOptions }
}

/**
 * 发送请求
 * @param url 请求 URL
 * @param options 请求选项
 */
export async function fetchService<T = any>(
  url: string,
  options: RequestOptions = {},
): Promise<ApiResponse<T>> {
  const { url: finalUrl, options: finalOptions } = buildRequest(url, options)

  // 处理超时
  const timeout = finalOptions.timeout || 30000
  delete finalOptions.timeout

  try {
    // 创建带超时的 Promise
    const timeoutPromise = new Promise<Response>((_, reject) => {
      setTimeout(() => reject(new Error(`Request timeout after ${timeout}ms`)), timeout)
    })

    // 创建 fetch Promise
    const fetchPromise = fetch(finalUrl, finalOptions)

    // 竞争两个 Promise
    const response = await Promise.race([fetchPromise, timeoutPromise])

    // 将响应转换为 JSON
    const data = await response.json()

    // 应用响应拦截器
    const processedData = responseInterceptor ? responseInterceptor(data) : data

    // 返回格式化后的响应
    return {
      code: processedData.code || response.status,
      message: processedData.message || response.statusText,
      data: processedData.data,
      success: response.ok && (!processedData.code || processedData.code === 200),
    }
  } catch (error) {
    // 应用错误拦截器
    if (errorInterceptor) {
      return errorInterceptor(error)
    }

    // 默认错误处理
    console.error('Fetch error:', error)
    return {
      code: 500,
      message: error instanceof Error ? error.message : 'Unknown error',
      data: {} as T,
      success: false,
    }
  }
}

/**
 * 注册服务
 * @param serviceName 服务名称
 * @param methods 服务方法
 */
export function registerService(
  serviceName: string,
  methods: Record<string, (...args: any[]) => Promise<any>>,
): void {
  if (serviceRegistry[serviceName]) {
    console.warn(`Service ${serviceName} already registered. It will be overwritten.`)
  }

  serviceRegistry[serviceName] = {
    name: serviceName,
    methods,
  }
}

/**
 * 获取服务
 * @param serviceName 服务名称
 */
export function getService(serviceName: string): ServiceDefinition | null {
  return serviceRegistry[serviceName] || null
}

/**
 * 调用服务方法
 * @param serviceName 服务名称
 * @param methodName 方法名称
 * @param args 方法参数
 */
export async function callServiceMethod<T = any>(
  serviceName: string,
  methodName: string,
  ...args: any[]
): Promise<T> {
  const service = getService(serviceName)

  if (!service) {
    throw new Error(`Service ${serviceName} not found`)
  }

  const method = service.methods[methodName]

  if (!method) {
    throw new Error(`Method ${methodName} not found in service ${serviceName}`)
  }

  return await method(...args)
}

/**
 * 创建 API 服务
 * @param baseURL 基础 URL
 */
export function createApiService(baseURL: string) {
  return {
    get: <T = any>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> => {
      const url = baseURL + endpoint
      return fetchService<T>(url, { ...options, method: 'GET' })
    },

    post: <T = any>(
      endpoint: string,
      data: any,
      options: RequestOptions = {},
    ): Promise<ApiResponse<T>> => {
      const url = baseURL + endpoint
      return fetchService<T>(url, {
        ...options,
        method: 'POST',
        body: JSON.stringify(data),
      })
    },

    put: <T = any>(
      endpoint: string,
      data: any,
      options: RequestOptions = {},
    ): Promise<ApiResponse<T>> => {
      const url = baseURL + endpoint
      return fetchService<T>(url, {
        ...options,
        method: 'PUT',
        body: JSON.stringify(data),
      })
    },

    patch: <T = any>(
      endpoint: string,
      data: any,
      options: RequestOptions = {},
    ): Promise<ApiResponse<T>> => {
      const url = baseURL + endpoint
      return fetchService<T>(url, {
        ...options,
        method: 'PATCH',
        body: JSON.stringify(data),
      })
    },

    delete: <T = any>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> => {
      const url = baseURL + endpoint
      return fetchService<T>(url, { ...options, method: 'DELETE' })
    },
  }
}
