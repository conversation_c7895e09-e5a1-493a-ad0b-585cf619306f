/**
 * 微前端应用实例管理器
 */
interface MicroAppInstance {
  state: Record<string, any>
  subscribers: Set<(state: any) => void>
  lastUpdated: number
}

export class MicroAppManager {
  private instances = new Map<string, MicroAppInstance>()
  
  /**
   * 获取或创建应用实例
   */
  getInstance(appId: string): MicroAppInstance {
    if (!this.instances.has(appId)) {
      this.instances.set(appId, {
        state: {},
        subscribers: new Set(),
        lastUpdated: Date.now(),
      })
    }
    return this.instances.get(appId)!
  }
  
  /**
   * 更新应用状态
   */
  updateState(appId: string, newState: Record<string, any>): void {
    const instance = this.getInstance(appId)
    const oldState = instance.state
    instance.state = { ...oldState, ...newState }
    instance.lastUpdated = Date.now()
    
    // 通知所有订阅者
    instance.subscribers.forEach((callback) => {
      try {
        callback(instance.state)
      } catch (error) {
        console.error(`Error in state subscriber for app ${appId}:`, error)
      }
    })
  }
  
  /**
   * 订阅应用状态变化
   */
  subscribe(appId: string, callback: (state: any) => void): () => void {
    const instance = this.getInstance(appId)
    instance.subscribers.add(callback)
    
    // 立即触发一次回调，返回当前状态
    callback(instance.state)
    
    // 返回取消订阅函数
    return () => {
      instance.subscribers.delete(callback)
    }
  }
  
  /**
   * 获取应用当前状态
   */
  getState(appId: string): Record<string, any> {
    return this.getInstance(appId).state
  }
  
  /**
   * 销毁应用实例
   */
  destroyInstance(appId: string): void {
    const instance = this.instances.get(appId)
    if (instance) {
      instance.subscribers.clear()
      this.instances.delete(appId)
    }
  }
  
  /**
   * 获取所有应用列表
   */
  getAllAppIds(): string[] {
    return Array.from(this.instances.keys())
  }
  
  /**
   * 批量更新多个应用的状态
   */
  broadcastToAll(newState: Record<string, any>): void {
    this.instances.forEach((_, appId) => {
      this.updateState(appId, newState)
    })
  }
} 