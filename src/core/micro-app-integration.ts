import '../types/micro-app'
import { microAppManager } from './react-provider'

/**
 * micro-app 集成工具类
 */
export class MicroAppIntegration {
  /**
   * 获取当前micro-app应用名称
   */
  static getCurrentAppName(): string | undefined {
    return window.__MICRO_APP_NAME__
  }

  /**
   * 检查是否在micro-app环境中运行
   */
  static isMicroAppEnvironment(): boolean {
    return !!window.microApp && !!window.__MICRO_APP_NAME__
  }

  /**
   * 从micro-app获取主应用传递的数据
   */
  static getDataFromMain(): any {
    if (window.microApp?.getData) {
      try {
        return window.microApp.getData() || {}
      } catch (error) {
        console.warn('Failed to get data from micro-app:', error)
        return {}
      }
    }
    return {}
  }

  /**
   * 向主应用发送数据
   */
  static dispatchToMain(data: any): void {
    if (window.microApp?.dispatch) {
      try {
        window.microApp.dispatch(data)
      } catch (error) {
        console.error('Failed to dispatch data to main app:', error)
      }
    } else {
      console.warn('micro-app dispatch not available')
    }
  }

  /**
   * 监听来自主应用的数据变化
   */
  static addDataListener(
    callback: (data: any) => void, 
    autoTrigger = false
  ): () => void {
    if (window.microApp?.addDataListener) {
      try {
        window.microApp.addDataListener(callback, autoTrigger)
        
        return () => {
          if (window.microApp?.removeDataListener) {
            window.microApp.removeDataListener(callback)
          }
        }
      } catch (error) {
        console.error('Failed to add data listener:', error)
      }
    }
    
    // 返回空的清理函数
    return () => {}
  }

  /**
   * 清除所有数据监听器
   */
  static clearAllDataListeners(): void {
    if (window.microApp?.clearDataListener) {
      try {
        window.microApp.clearDataListener()
      } catch (error) {
        console.error('Failed to clear data listeners:', error)
      }
    }
  }

  /**
   * 同步micro-app数据到SDK状态管理器
   */
  static syncToSDK(appId?: string): void {
    const currentAppId = appId || this.getCurrentAppName()
    if (!currentAppId) {
      console.warn('No app ID available for syncing')
      return
    }

    const microAppData = this.getDataFromMain()
    if (Object.keys(microAppData).length > 0) {
      microAppManager.updateState(currentAppId, microAppData)
    }
  }

  /**
   * 设置自动同步：将micro-app数据变化自动同步到SDK
   */
  static setupAutoSync(appId?: string): () => void {
    const currentAppId = appId || this.getCurrentAppName()
    if (!currentAppId) {
      console.warn('No app ID available for auto sync')
      return () => {}
    }

    // 立即同步一次
    this.syncToSDK(currentAppId)

    // 监听后续变化
    return this.addDataListener((data) => {
      microAppManager.updateState(currentAppId, data)
    }, false)
  }
}

/**
 * 便捷的导出函数
 */

/**
 * 检查是否在micro-app环境中
 */
export const isMicroApp = MicroAppIntegration.isMicroAppEnvironment

/**
 * 获取当前应用名
 */
export const getCurrentAppName = MicroAppIntegration.getCurrentAppName

/**
 * 向主应用发送数据
 */
export const dispatchToMain = MicroAppIntegration.dispatchToMain

/**
 * 监听主应用数据
 */
export const addDataListener = MicroAppIntegration.addDataListener

/**
 * 同步数据到SDK
 */
export const syncToSDK = MicroAppIntegration.syncToSDK

/**
 * 设置自动同步
 */
export const setupAutoSync = MicroAppIntegration.setupAutoSync 