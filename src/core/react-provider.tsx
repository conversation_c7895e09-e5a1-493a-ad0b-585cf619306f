import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { MicroAppManager } from './micro-app-manager'
import mainAppSDK from '../index'
import '../types/micro-app'

/**
 * 微前端应用Context的值类型
 */
interface MicroAppContextValue {
  appId: string
  state: Record<string, any>
  updateState: (newState: Record<string, any>) => void
  callMainMethod: (serviceName: string, methodName: string, ...args: any[]) => any
  sendMessage: (targetApp: string, data: any) => void
  broadcastMessage: (data: any) => void
}

/**
 * 微前端应用Context
 */
const MicroAppContext = createContext<MicroAppContextValue | null>(null)

/**
 * Provider组件的Props类型
 */
interface MicroAppProviderProps {
  appId: string
  children: React.ReactNode
  initialData?: Record<string, any>
  onError?: (error: Error) => void
  onStateChange?: (state: Record<string, any>) => void
}

/**
 * 微前端应用管理器实例
 */
const microAppManager = new MicroAppManager()

/**
 * 微前端应用Provider组件
 */
export function MicroAppProvider({
  appId: providedAppId,
  children,
  initialData = {},
  onError,
  onStateChange,
}: MicroAppProviderProps) {
  // 优先使用 micro-app 的应用名称，否则使用传入的 appId
  const appId = window.__MICRO_APP_NAME__ || providedAppId
  
  const [state, setState] = useState(() => {
    // 1. 先尝试从 micro-app 获取数据
    let microAppData = {}
    if (window.microApp?.getData) {
      try {
        microAppData = window.microApp.getData() || {}
      } catch (error) {
        console.warn('Failed to get data from micro-app:', error)
      }
    }
    
    // 2. 获取SDK管理的状态
    const currentState = microAppManager.getState(appId)
    
    // 3. 合并数据：micro-app数据 + 初始数据 + SDK状态
    const initialState = { ...microAppData, ...initialData, ...currentState }
    
    // 4. 更新到管理器
    if (Object.keys(initialState).length > 0) {
      microAppManager.updateState(appId, initialState)
    }
    
    return initialState
  })

  // 更新本地状态的函数
  const updateState = useCallback((newState: Record<string, any>) => {
    try {
      microAppManager.updateState(appId, newState)
    } catch (error) {
      onError?.(error as Error)
    }
  }, [appId, onError])

  // 调用主应用方法
  const callMainMethod = useCallback((serviceName: string, methodName: string, ...args: any[]) => {
    try {
      return mainAppSDK.callMainMethod(serviceName, methodName, ...args)
    } catch (error) {
      onError?.(error as Error)
      return null
    }
  }, [onError])

  // 发送消息到其他应用（支持 micro-app 和 SDK 两种方式）
  const sendMessage = useCallback((targetApp: string, data: any) => {
    try {
      // 同时使用两种方式发送，确保兼容性
      mainAppSDK.message.sendToApp(targetApp, data)
      
      // 如果是发送给主应用，使用 micro-app 的 dispatch
      if (targetApp === 'main' && window.microApp?.dispatch) {
        window.microApp.dispatch(data)
      }
    } catch (error) {
      onError?.(error as Error)
    }
  }, [onError])

  // 广播消息（同时使用 SDK 和 micro-app）
  const broadcastMessage = useCallback((data: any) => {
    try {
      // 1. 通过SDK广播
      mainAppSDK.message.broadcast(data)
      
      // 2. 通过 micro-app 发送给主应用
      if (window.microApp?.dispatch) {
                 window.microApp.dispatch({
           type: 'broadcast',
           data,
           from: appId,
           timestamp: Date.now(),
         })
      }
    } catch (error) {
      onError?.(error as Error)
    }
  }, [onError, appId])

  // 监听应用状态变化
  useEffect(() => {
    const unsubscribe = microAppManager.subscribe(appId, (newState) => {
      setState(newState)
      onStateChange?.(newState)
    })

    return unsubscribe
  }, [appId, onStateChange])

  // 监听主应用的全局消息和 micro-app 数据
  useEffect(() => {
    const unsubscribers: (() => void)[] = []

    // 1. 监听SDK的全局消息
    const sdkUnsubscribe = mainAppSDK.message.listenGlobal((data) => {
      updateState(data)
    })
    unsubscribers.push(sdkUnsubscribe)

    // 2. 监听 micro-app 的数据变化
    if (window.microApp?.addDataListener) {
             const microAppListener = (data: any) => {
         try {
           updateState(data)
         } catch (error) {
           onError?.(error as Error)
         }
       }
       
       window.microApp.addDataListener(microAppListener, true) // autoTrigger = true
       
       unsubscribers.push(() => {
         if (window.microApp?.removeDataListener) {
           window.microApp.removeDataListener(microAppListener)
         }
       })
    }

         return () => {
       unsubscribers.forEach((unsubscribe) => unsubscribe())
     }
  }, [updateState, onError])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 注意：这里不直接销毁实例，因为可能有多个组件使用同一个appId
      // 实例的生命周期由外部控制
    }
  }, [])

  const contextValue: MicroAppContextValue = {
    appId,
    state,
    updateState,
    callMainMethod,
    sendMessage,
    broadcastMessage,
  }

  return (
    <MicroAppContext.Provider value={contextValue}>
      {children}
    </MicroAppContext.Provider>
  )
}

/**
 * 使用微前端应用Context的Hook
 */
export function useMicroApp(): MicroAppContextValue {
  const context = useContext(MicroAppContext)
  
  if (!context) {
    throw new Error('useMicroApp must be used within a MicroAppProvider')
  }
  
  return context
}

/**
 * 使用微前端应用状态的Hook
 */
export function useMicroAppState<T = any>(key?: string): T | Record<string, any> {
  const { state } = useMicroApp()
  
  if (key) {
    return state[key] as T
  }
  
  return state as T
}

/**
 * 导出管理器实例，供外部使用
 */
export { microAppManager } 